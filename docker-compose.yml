version: "3.9"
services:
  postgres:
    image: postgres:15
    container_name: postgres-keycloak
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: keycloak_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - keycloak-network

  keycloak:
    image: quay.io/keycloak/keycloak:24.0.4
    container_name: keycloak
    depends_on:
      - postgres
    ports:
      - "8086:8080"
    environment:
      KEYCLOAK_ADMIN: admin
      KEY<PERSON>OAK_ADMIN_PASSWORD: admin_password
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloak_pass
      KC_HOSTNAME: localhost
    volumes:
      - keycloak_data:/opt/keycloak/data
    command: start-dev
    networks:
      - keycloak-network

volumes:
  postgres_data:
  keycloak_data:

networks:
  keycloak-network:
    driver: bridge
